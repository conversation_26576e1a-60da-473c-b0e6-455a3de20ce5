<?php

namespace GalaxyAPI\Controllers;

use App\Model\Employer;
use GalaxyAPI\Requests\EmployerRequest;
use GalaxyAPI\Requests\EmptyRequest;
use GalaxyAPI\Resources\EmployerResource;
use Illuminate\Support\Facades\Auth;
use Config;

class EmployerController extends CrudBaseController
{
    public function __construct()
    {
        $this->withAll = [
            'industry',
        ];
        $this->loadAll = [
            'industry',
        ];
        $this->scopeWithValue = [
            'collegeId' => Auth::user()?->college_id,
        ];
        
        parent::__construct(
            model: Employer::class,
            storeRequest: EmployerRequest::class,
            updateRequest: EmployerRequest::class,
            resource: EmployerResource::class,
        );
    }

    public function formConstants()
    {
        $arrStatus = Config::get('constants.arrStatus');
        $arrIndustries = Config::get('constants.arrTraningOrgazinationIndustryCode');
        $arrCountries = Config::get('constants.arrCountry');

        return ajaxSuccess([
            'status_options' => $arrStatus,
            'industry_options' => $arrIndustries,
            'country_options' => $arrCountries,
        ], '');
    }
}
