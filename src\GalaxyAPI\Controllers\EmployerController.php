<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\Country;
use App\Model\v2\Employer;
use Config;
use GalaxyAPI\Requests\EmployerRequest;
use GalaxyAPI\Resources\EmployerResource;
use Illuminate\Support\Facades\Auth;

class EmployerController extends CrudBaseController
{
    public function __construct()
    {
        $this->withAll = [
            'industry',
        ];
        $this->loadAll = [
            'industry',
        ];
        $this->scopeWithValue = [
            'collegeId' => Auth::user()?->college_id,
        ];

        parent::__construct(
            model: Employer::class,
            storeRequest: EmployerRequest::class,
            updateRequest: EmployerRequest::class,
            resource: EmployerResource::class,
        );
    }

    public function formConstants()
    {
        $collegeId = Auth::user()?->college_id;
        $arrStatus = Config::get('constants.arrStatus');
        $arrIndustries = Config::get('constants.arrTraningOrgazinationIndustryCode');
        $arrCountries = Country::whereIn('college_id', [$collegeId, 0])->orderBy('name')->pluck('name', 'id')->toArray();

        return ajaxSuccess([
            'status_options' => $arrStatus,
            'industry_options' => $arrIndustries,
            'country_options' => $arrCountries,
        ], '');
    }
}
