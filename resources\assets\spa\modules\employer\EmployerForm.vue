<template>
    <AsyncForm
        :form-config="formFields"
        :initial-values="store.formData"
        @submit="onSubmit"
        @change="onChange"
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        :dialogTitle="'Employer Management'"
        :override="true"
        :store="store"
    >
        <EmployerFormContent />
    </AsyncForm>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useEmployerStore } from '@spa/stores/modules/employer/employerStore.js';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncForm.vue';
import EmployerFormContent from '@spa/modules/employer/partials/EmployerFormContent.vue';

const store = useEmployerStore();

const formFields = computed(() => [
    {
        name: 'employer_name',
        label: 'Employer Name',
        type: 'text',
        required: true,
        validation: 'required',
        col: 6,
    },
    {
        name: 'trading_name',
        label: 'Trading Name',
        type: 'text',
        required: true,
        validation: 'required',
        col: 6,
    },
    {
        name: 'contact_person',
        label: 'Contact Person',
        type: 'text',
        required: true,
        validation: 'required',
        col: 6,
    },
    {
        name: 'ABN',
        label: 'ABN',
        type: 'text',
        required: true,
        validation: 'required',
        col: 6,
    },
    {
        name: 'address',
        label: 'Address',
        type: 'textarea',
        required: true,
        validation: 'required',
        col: 12,
    },
    {
        name: 'industries_id',
        label: 'Industry',
        type: 'select',
        required: false,
        options: store.industryOptions,
        col: 6,
    },
    {
        name: 'suburb',
        label: 'Suburb',
        type: 'text',
        required: true,
        validation: 'required',
        col: 6,
    },
    {
        name: 'country_id',
        label: 'Country',
        type: 'select',
        required: false,
        options: store.countryOptions,
        col: 6,
    },
    {
        name: 'email',
        label: 'Email',
        type: 'email',
        required: true,
        validation: 'required|email',
        col: 6,
    },
    {
        name: 'fax',
        label: 'Fax',
        type: 'text',
        required: false,
        col: 6,
    },
    {
        name: 'status',
        label: 'Status',
        type: 'select',
        required: true,
        validation: 'required',
        options: store.statusOptions,
        col: 6,
    },
    {
        name: 'postcode',
        label: 'Postcode',
        type: 'text',
        required: true,
        validation: 'required',
        col: 6,
    },
    {
        name: 'state',
        label: 'State',
        type: 'text',
        required: false,
        col: 6,
    },
    {
        name: 'phone',
        label: 'Phone',
        type: 'text',
        required: true,
        validation: 'required',
        col: 6,
    },
    {
        name: 'mobile',
        label: 'Mobile',
        type: 'text',
        required: false,
        col: 6,
    },
]);

const onSubmit = () => {
    store.submitKendoForm();
};

const onChange = (data) => {
    store.formData = { ...store.formData, ...data };
};

onMounted(async () => {
    await store.getFormConstants();
});
</script>
