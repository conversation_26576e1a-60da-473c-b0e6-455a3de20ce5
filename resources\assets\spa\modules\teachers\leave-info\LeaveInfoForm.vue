<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="center"
        :dialogTitle="'Add Leave Info'"
        :store="store"
        maxWidth="400px"
    >
        <div class="p-4">
            <div class="p-2">
                <FormDatePicker
                    type="data"
                    name="from_date"
                    label="From Date"
                    v-model="formData.from_date"
                    :format="'dd-MM-yyyy'"
                    :emit-format="'yyyy-MM-dd'"
                    :validation-message="store.errors?.from_date"
                    :valid="!store.errors?.from_date"
                    :touched="true"
                    :indicaterequired="true"
                />
            </div>
            <div class="p-2">
                <FormDatePicker
                    type="data"
                    name="to_date"
                    label="To Date"
                    v-model="formData.to_date"
                    :validation-message="store.errors?.to_date"
                    :valid="!store.errors?.to_date"
                    :touched="true"
                    :format="'dd-MM-yyyy'"
                    :emit-format="'yyyy-MM-dd'"
                    :indicaterequired="true"
                />
            </div>
            <div class="p-2">
                <FormTextArea
                    type="data"
                    name="comment"
                    label="Details"
                    v-model="formData.comment"
                    :validation-message="store.errors?.comment"
                    :valid="!store.errors?.comment"
                    :touched="true"
                    :indicaterequired="true"
                    :rows="4"
                />
            </div>
        </div>
    </AsyncForm>
</template>
<script setup>
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncForm.vue';
import { useLeaveInfoStore } from '@spa/stores/modules/teachers/useLeaveInfoStore.js';
import { storeToRefs } from 'pinia';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
const store = useLeaveInfoStore();
const { formData } = storeToRefs(store);
</script>
