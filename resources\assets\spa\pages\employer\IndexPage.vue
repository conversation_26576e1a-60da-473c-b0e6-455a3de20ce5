<script setup>
import EmployerListComponent from '@spa/modules/employer/EmployerListComponent.vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
</script>

<template>
    <Layout :no-spacing="true">
        <Head title="Manage Employer" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Manage Employer" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <EmployerListComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
