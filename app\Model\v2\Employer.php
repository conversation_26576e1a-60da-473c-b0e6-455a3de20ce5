<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;
use Support\Traits\CreaterUpdaterTrait;

class Employer extends Model
{
    use CreaterUpdaterTrait;

    protected $table = 'rto_employer';

    protected $fillable = [
        'college_id',
        'employer_name',
        'trading_name',
        'contact_person',
        'ABN',
        'industries_id',
        'address',
        'suburb',
        'country_id',
        'email',
        'fax',
        'postcode',
        'state',
        'phone',
        'mobile',
        'status',
    ];

    public function getEmployerNameList($collegeId)
    {
        $arrEmployerList = Employer::where('college_id', '=', $collegeId)->get(['id as Id', 'employer_name as Name'])->toArray();

        // $result[''] = '- - No Employer - -';
        // foreach ($arrEmployerList as $row) {
        //     $result[$row->id] = $row->employer_name;
        // }
        return $arrEmployerList;
    }

    // Scope for filtering by college
    public function scopeCollegeId($query, $collegeId)
    {
        return $query->where('college_id', $collegeId);
    }

    // Relationship with industry
    public function industry()
    {
        return $this->belongsTo(\App\Model\Industries::class, 'industries_id');
    }
}
