<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;
use Auth;
use Config;
use Support\Traits\CreaterUpdaterTrait;

class Employer extends Model {

    use CreaterUpdaterTrait;

    protected $table = 'rto_employer';

    protected $fillable = [
        'college_id',
        'employer_name',
        'trading_name',
        'contact_person',
        'ABN',
        'industries_id',
        'address',
        'suburb',
        'country_id',
        'email',
        'fax',
        'postcode',
        'state',
        'phone',
        'mobile',
        'status',
    ];

    // Scope for filtering by college
    public function scopeCollegeId($query, $collegeId)
    {
        return $query->where('college_id', $collegeId);
    }

    // Relationship with industry
    public function industry()
    {
        return $this->belongsTo(\App\Model\Industries::class, 'industries_id');
    }

    public function saveEmployerDetail($collegeId, $request) {

        $objEmployer = new Employer();
        $objEmployer->college_id = $collegeId;
        $objEmployer->employer_name = ($request->input('employer_name') != '') ? $request->input('employer_name') : null;
        $objEmployer->trading_name = ($request->input('trading_name') != '') ? $request->input('trading_name') : null;
        $objEmployer->contact_person = ($request->input('contact_person') != '') ? $request->input('contact_person') : null;
        $objEmployer->ABN = ($request->input('ABN') != '') ? $request->input('ABN') : null;
        $objEmployer->industries_id = ($request->input('industries_id') != '') ? $request->input('industries_id') : null;
        $objEmployer->address = ($request->input('address') != '') ? $request->input('address') : null;
        $objEmployer->suburb = ($request->input('suburb') != '') ? $request->input('suburb') : null;
        $objEmployer->country_id = ($request->input('country_id') != '') ? $request->input('country_id') : null;
        $objEmployer->email = ($request->input('email') != '') ? $request->input('email') : null;
        $objEmployer->fax = ($request->input('fax') != '') ? $request->input('fax') : null;
        $objEmployer->postcode = ($request->input('postcode') != '') ? $request->input('postcode') : null;
        $objEmployer->state = ($request->input('state') != '') ? $request->input('state') : null;
        $objEmployer->phone = ($request->input('phone') != '') ? $request->input('phone') : null;
        $objEmployer->mobile = ($request->input('mobile') != '') ? $request->input('mobile') : null;
        $objEmployer->status = ($request->input('status') != '') ? $request->input('status') : null;
        $objEmployer->created_by = Auth::user()->id;
        $objEmployer->updated_by = Auth::user()->id;
        $objEmployer->save();
    }

    public function editEmployerDetail($employerId, $request) {

        $objEmployer = Employer::find($employerId);
        $objEmployer->employer_name = ($request->input('employer_name') != '') ? $request->input('employer_name') : null;
        $objEmployer->trading_name = ($request->input('trading_name') != '') ? $request->input('trading_name') : null;
        $objEmployer->contact_person = ($request->input('contact_person') != '') ? $request->input('contact_person') : null;
        $objEmployer->ABN = ($request->input('ABN') != '') ? $request->input('ABN') : null;
        $objEmployer->industries_id = ($request->input('industries_id') != '') ? $request->input('industries_id') : null;
        $objEmployer->address = ($request->input('address') != '') ? $request->input('address') : null;
        $objEmployer->suburb = ($request->input('suburb') != '') ? $request->input('suburb') : null;
        $objEmployer->country_id = ($request->input('country_id') != '') ? $request->input('country_id') : null;
        $objEmployer->email = ($request->input('email') != '') ? $request->input('email') : null;
        $objEmployer->fax = ($request->input('fax') != '') ? $request->input('fax') : null;
        $objEmployer->postcode = ($request->input('postcode') != '') ? $request->input('postcode') : null;
        $objEmployer->state = ($request->input('state') != '') ? $request->input('state') : null;
        $objEmployer->phone = ($request->input('phone') != '') ? $request->input('phone') : null;
        $objEmployer->mobile = ($request->input('mobile') != '') ? $request->input('mobile') : null;
        $objEmployer->status = ($request->input('status') != '') ? $request->input('status') : null;
        $objEmployer->updated_by = Auth::user()->id;
        $objEmployer->save();
    }

    public function getEmployerlist($collegeId, $perPage) {
        return Employer::leftjoin('rto_industries as ri', 'ri.id', '=', 'rto_employer.industries_id')
                        ->where('rto_employer.college_id', '=', $collegeId)
                        ->select('ri.name as industriy_name', 'rto_employer.*')
                        ->paginate($perPage);
    }

//    Data Table Changes Start chetan
    public function getEmployerlistnew($collegeId, $request) {
        $arrStatus = Config::get('constants.arrStatus');
        $arrIndustries = Config::get('constants.arrTraningOrgazinationIndustryCode');
        $requestData = $_REQUEST;
        $columns = array(
            // datatable column index  => database column name
            0 => 're.employer_name',
            1 => 're.contact_person',
            2 => 're.email',
            3 => 're.status',
            4 => 're.industries_id',
            5 => 're.phone',
            6 => 're.trading_name',
            7 => 're.state',
        );

        $query = Employer::from('rto_employer as re')
                ->leftjoin('rto_industries as ri', 'ri.id', '=', 're.industries_id')
                ->where('re.college_id', '=', $collegeId);

        if (!empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];
            $query->where(function($query) use ($columns, $searchVal, $requestData, $arrIndustries) {
                        $flag = 0;
                        foreach ($columns as $key => $value) {
                            $searchVal = $requestData['search']['value'];
                            if ($key == 3 && ($searchVal == 'Active' || $searchVal == 'active')) {
                                $searchVal = 1;
                            } else if ($key == 3 && ($searchVal == 'InActive' || $searchVal == 'inactive')) {
                                $searchVal = 2;
                            }
                            $results = array();
                            if ($key == 4) {
                                $results = array_filter($arrIndustries, function($value) use($searchVal) {
                                            return strpos(strtolower($value), strtolower($searchVal)) !== false;
                                        });
                                $results = array_keys($results);
                            }
                            if ($requestData['columns'][$key]['searchable'] == 'true') {
                                if ($flag == 0 && $key != 4) {
                                    $flag = $flag + 1;
                                    $query->where($value, 'like', '%' . $searchVal . '%');
                                } else if (!empty($results) && $key == 4) {
                                    $query->orWhere(function($subquery) use ($value, $results) {
                                                $subquery->whereIn($value, $results);
                                            });
                                } else {
//                                    $query->orWhere($value, 'like',"%$searchVal%");
                                    $query->orWhere($value, 'like',"$searchVal%");
                                }
                            }
                        }
                    });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());
        $resultArr = $query->skip($requestData['start'])
                ->take($requestData['length'])
                ->select('re.employer_name', 're.contact_person', 're.email', 're.status', 'ri.name as industriy_name', 're.phone', 're.id', 're.trading_name', 're.state', 're.industries_id')
                ->get();

        $data = array();

        foreach ($resultArr as $row) {
            $nestedData = array();
            $actionHtml = '';

            $actionHtml .= ' <li><a class="edit-enrollment-fee link-black text-sm" data-toggle="tooltip" data-original-title="Edit" href="edit-employer/' . $row["id"] . '"><i class="fa fa-edit "></i></a></li>';
            $actionHtml .= ' <li><span data-toggle="modal" class="delete" data-id="' . $row["id"] . '" data-target="#deleteModal"> 
                                                    <a class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove "></i></a> </span></li>';
            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                                <li>' . $actionHtml . '</li>
                            </ul>
                        </div>';
            $nestedData[] = $row["employer_name"] . $action;
            $nestedData[] = $row["contact_person"];
            $nestedData[] = $row["email"];
            $nestedData[] = !empty($row['status']) ? ((isset($arrStatus[$row['status']]) && $arrStatus[$row['status']] != '--Select Status--') ? $arrStatus[$row->status] : '--') : '--';
            $nestedData[] = (isset($arrIndustries[$row['industries_id']]) ? $arrIndustries[$row['industries_id']] : '--');
            $nestedData[] = $row['phone'];
            $nestedData[] = $row['trading_name'];
            $nestedData[] = $row['state'];
            $data[] = $nestedData;
        }

        $json_data = array(
            "draw" => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw. 
            "recordsTotal" => intval($totalData), // total number of records
            "recordsFiltered" => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            "data" => $data   // total data array
        );
        return $json_data;
    }

//    Data Table Changs END chetan
    //edit time
    public function getEmployerData($employerId) {
        return Employer::where('id', '=', $employerId)->get();
    }

    public function deleteEmployerDetail($employerId, $collegeId) {
        return Employer::where('id', '=', $employerId)->where('college_id', '=', $collegeId)->delete();
    }

    public function _searchByString($searchBy, $searchString, $searchStatus, $collegeId) {

        $searchEmployerData = Employer::leftjoin('rto_industries as ri', 'ri.id', '=', 'rto_employer.industries_id')
                ->select('ri.name as industry_name', 'rto_employer.*')
                ->where('rto_employer.college_id', '=', $collegeId);
        if ($searchBy == 'employer_name') {
            $searchEmployerData->where('rto_employer.employer_name', 'like', '%' . $searchString . '%');
        }
        if ($searchBy == 'email') {
            $searchEmployerData->where('rto_employer.email', 'like', '%' . $searchString . '%');
        }
        if ($searchBy == 'industries_id') {
            $searchEmployerData->where('ri.name', 'like', '%' . $searchString . '%');
        }
        if ($searchBy == 'status') {
            $searchEmployerData->where('rto_employer.status', '=', $searchStatus);
        }
        return $searchEmployerData->get();
    }

    public function getEmployerNameList($collegeId) {

        $arrEmployerList = Employer::where('college_id', '=', $collegeId)
                ->get(['id', 'employer_name']);
        //->toArray();

        $result[''] = '- - No Employer - -';
        foreach ($arrEmployerList as $row) {
            $result[$row->id] = $row->employer_name;
        }
        return $result;
    }

    public function getEmployerNameListV2($collegeId) {

        $arrEmployerList = Employer::where('college_id', '=', $collegeId)
                ->get(['id', 'employer_name']);
        //->toArray();
        $nullRecord[''] = 'No Employer Found';
        foreach ($arrEmployerList as $row) {
            $result[$row->id] = $row->employer_name;
        }
        if (count($arrEmployerList) == 0) {
            return $nullRecord;
        } else {
            return $result;
        }
    }

    public function getAllEmployerList($collegeId) {
        return Employer::from('rto_employer as ra')
                        ->leftjoin('rto_industries as ri', 'ri.id', '=', 'ra.industries_id')
                        ->where('ra.college_id', $collegeId)
                        ->get(['ri.name as industriy_name', 'ra.*']);
    }

    public function getFilterEmployerListInfo($collegeId, $searchBy, $status) {
        //echo $searchBy;exit;
        $sql = Employer::from('rto_employer as ra')
                ->leftjoin('rto_industries as ri', 'ri.id', '=', 'ra.industries_id')
                ->where('ra.college_id', $collegeId);

        if (!empty($searchBy) && !empty($status)) {
            $sql->where('ra.status', $status);
        }

        $result = $sql->get(['ri.name as industriy_name', 'ra.*']);
        return $result;
    }

    public function getEmployerResendRequest($collegeId, $formId) {

        return Employer::from('rto_employer as ra')
                        ->leftjoin('rto_survey_send_request as rssr', 'rssr.employee_id', '=', 'ra.id')
                        ->where('ra.college_id', $collegeId)
                        ->where('rssr.form_name_id', $formId)
                        ->groupBy('ra.id')
                        ->get(['ra.id',
                            'ra.employer_name',
                            'ra.email',
                            'ra.status',
                            'rssr.group_id',
                            'rssr.supervisor',
                            'rssr.before_submit',
                            'rssr.created_at']);
    }

    public function getEmployerArr($collegeId) {
        $nullRecord[''] = 'No Employer Found';
        $defaultArr[''] = '- - Select Employer - -';
        $dataArr = Employer::where('college_id', $collegeId)->pluck('employer_name', 'id')->toArray();
        if (count($dataArr) > 0) {
            return $result = $defaultArr + $dataArr;
        } else {
            return $nullRecord;
        }
    }

}
