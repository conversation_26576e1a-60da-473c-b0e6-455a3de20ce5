<?php

namespace App\Exceptions;

use Auth;
use Exception;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Support\DTO\ReporterData;
use Support\LogAlarm\ExceptionReporter;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that should not be reported.
     *
     * @var array
     */
    protected $dontReport = [
        \Illuminate\Auth\AuthenticationException::class,
        \Illuminate\Auth\Access\AuthorizationException::class,
        \Symfony\Component\HttpKernel\Exception\HttpException::class,
        \Illuminate\Database\Eloquent\ModelNotFoundException::class,
        \Illuminate\Session\TokenMismatchException::class,
        \Illuminate\Validation\ValidationException::class,
    ];

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sentry, Bugsnag, etc.
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function report(Throwable $exception)
    {
        // dd($exception);
        if (config('logging.alarm.enabled') && ! in_array(get_class($exception), $this->dontReport)) {

            try {
                $url = url()->current();
                $input = request()->except(['_token', 'password', 'secret', 'components']);
                $userId = optional(auth()->user())->id;
            } catch (\Throwable $e) {
                // In case request() is no longer available
                $url = 'N/A';
                $input = [];
                $userId = null;
            }

            dispatch(new ExceptionReporter(ReporterData::LazyFromArray([
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
                'exception_class' => get_class($exception),
            ]), $url, $input, $userId));
        }
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Exception  $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, Throwable $exception)
    {
        Log::info('Exception being handled:', [
            'type' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
        ]);
        // dd($request->header('X-Inertia'));
        // dd($exception);
        //   return parent::render($request, $exception);
        // if ($exception instanceof MethodNotAllowedHttpException) {
        //     return response()->view('errors.error-exception', ['message' => $exception->getMessage()], 404);
        // }

        $isJsonRequest = request()->wantsJson() || request()->is('api/v3/*');

        if ($exception instanceof AuthenticationException && $isJsonRequest) {
            return response()->json([
                'code' => 403,
                'status' => 'error',
                'message' => $exception->getMessage(),
            ], 403);
        }

        if ($exception instanceof ApplicationException) {
            if (request()->ajax()) {
                return ajaxError($exception->message());
            }

            return redirect()->back()->with(['message' => $exception->message()]);
        }

        if ($exception instanceof \Stancl\Tenancy\Exceptions\TenantCouldNotBeIdentifiedOnDomainException) {
            abort(404);
        }

        if ($this->isHttpException($exception)) {
            $statusCode = $exception->getStatusCode() ?? 500;
            if ($isJsonRequest) {
                return response()->json([
                    'code' => $statusCode,
                    'status' => 'error',
                    'message' => $exception->getMessage(),
                ], $statusCode);
            }

            return $this->renderHttpException($exception);
        } else {
            if ($exception instanceof ValidationException) {
                if (request()->ajax() || $isJsonRequest) {
                    return response()->json([
                        'code' => $exception->status,
                        'status' => 'error',
                        'message' => count($exception->errors()) ? array_values($exception->errors())[0] : '',
                        'data' => $exception->errors(),
                    ], $exception->status);
                }

                return redirect()->back()
                    ->withErrors($exception->errors())->withInput();
            }

            if ($exception instanceof TokenMismatchException) {

                return redirect()->back()
                    ->with([
                        'error' => $exception->getMessage(),
                        'session_error' => $exception->getMessage(),
                    ])->withInput();
            }

            if (! app()->environment('local') && (request()->ajax() || $isJsonRequest)) {
                return response()->json([
                    'code' => $exception->status ?? 401,
                    'status' => 'error',
                    'message' => $exception->getMessage(),
                ]);
            }

            if (tenant() && Auth::guest()) {
                $request->session()->flash('session_error', 'Your session has expired. Please login again.');

                return redirect()->guest('login');
            }

            // Custom error 500 view on production
            if ($exception instanceof \PDOException) {
                // Log::error($exception->getTraceAsString());
                return response()->view('errors.error-exception', ['message' => $exception->getMessage(), 'trace' => $exception->getTraceAsString()], 404);
            }
            if ($exception instanceof \ErrorException && ! app()->environment('local')) {
                return response()->view('errors.error-exception', ['message' => $exception->getMessage(), 'trace' => $exception->getTraceAsString()], 404);
            }
            //            if ($exception instanceof TokenMismatchException) {
            //                if ($request->ajax()){
            //                    return redirect()->guest('login');
            //                }
            //                return redirect()->guest('login');
            //             }

            if (! app()->environment('local')) {
                if (request()->ajax()) {
                    return ajaxError($exception->getMessage());
                }

                return response()->view('errors.error-exception', ['message' => $exception->getMessage(), 'trace' => $exception->getTraceAsString()], 404);
            }

            //            }
        }

        return parent::render($request, $exception);
    }

    /**
     * Convert an authentication exception into an unauthenticated response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        if ($request->expectsJson()) {
            return response()->json(['error' => 'Unauthenticated.'], 401);
        }

        return redirect()->guest('login');
    }
}
