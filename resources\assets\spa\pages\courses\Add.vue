<template>
    <Layout :loading="true">
        <Head title="Search for courses to add" />
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Add Course'" :linkurl="getCourseListUrl" :back="true" />
        </template>
        <div class="border-1 mb-8 mt-4 w-full rounded-md border-gray-200 bg-white p-6 lg:w-11/12">
            <div class="mb-4 text-lg font-medium text-gray-900">
                Which course would you like to add?
            </div>
            <div class="mb-6 space-x-1 text-gray-500">
                <span
                    >Use below search box to search. Browse and find relevant course code from</span
                >
                <a
                    class="text-primary-blue-500 hover:text-primary-blue-400"
                    href="https://training.gov.au/Search"
                    target="_blank"
                    >https://training.gov.au/Search</a
                >
            </div>
            <div class="mb-6 flex justify-between">
                <div class="w-full max-w-xl md:w-1/2">
                    <div class="flex w-full">
                        <div class="searchCoursesToAdd relative w-full">
                            <span class="absolute left-3 top-1/2 -translate-y-1/2">
                                <icon :name="'lens'" width="16" height="16" v-if="!searching" />
                                <icon
                                    :name="'loading'"
                                    :width="16"
                                    :height="16"
                                    :fill="'#1890FF'"
                                    :stroke="'#E2E8F0'"
                                    v-else
                                />
                            </span>
                            <input
                                type="text"
                                id="searchCourse"
                                v-model="searchText"
                                @keypress="handleKeypressEvent"
                                v-debounce="300"
                                class="tw-input-text pl-8"
                                placeholder="Search by Course Code or Course Name"
                                autocomplete="off"
                            />
                        </div>
                        <div class="ml-2">
                            <PrimaryButton @click="SearchCourses">
                                <div class="uppercase">Search</div>
                            </PrimaryButton>
                        </div>
                    </div>
                    <div v-if="checkSearchTextValid" class="mt-1 text-sm text-red-500">
                        Search text should only have alphabets, numbers and/or space. No other
                        special characters allowed.
                    </div>
                </div>
            </div>
            <GlobalContextLoader
                context="tga-course-list"
                :overlay="false"
                :type="'skeleton-list'"
                :size="'large'"
            >
                <div v-if="courses.Count > 0" class="space-y-4">
                    <div class="text-gray-700">
                        Showing {{ courses.Count }} course{{ courses.Count > 1 ? 's' : '' }}
                        matching
                        {{ props.filters?.search }}
                    </div>
                    <div
                        v-if="hasShortCourse && !isShortCourseEnabled"
                        class="mb-4 flex items-center space-x-4 rounded-md bg-yellow-100 p-6 py-2 italic text-orange-700 shadow"
                    >
                        <icon :name="'warning'" :width="24" :height="24" :fill="'#a16207'" />
                        <div>
                            Search result contains some Skill Sets and Units. But, Short course is
                            not enabled from course type settings.
                            <a
                                :href="route('course-types')"
                                class="font-medium text-primary-blue-500"
                                target="_blank"
                                >Go to course type settings</a
                            >
                            to enable short courses.
                        </div>
                    </div>
                    <div
                        v-for="(courseItem, index) in courses.Results"
                        class="mt-4 rounded-md border border-gray-200 bg-gray-50 px-6 py-3"
                    >
                        <div class="flex justify-between">
                            <div class="flex">
                                <div class="mr-2">
                                    <svg
                                        width="48"
                                        height="48"
                                        viewBox="0 0 48 48"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M14.4 11.7C14.4 10.5597 15.2059 9.63528 16.2 9.63528H30.6C31.5941 9.63528 32.4 10.5597 32.4 11.7V15.8294C32.4 16.9697 31.5941 17.8941 30.6 17.8941H16.2C15.2059 17.8941 14.4 16.9697 14.4 15.8294V11.7ZM17.1 14.797H29.7V12.7323H17.1V14.797ZM9 8.60293C9 5.75216 11.0147 3.44116 13.5 3.44116H34.2C36.6853 3.44116 38.7 5.75216 38.7 8.60293V38.025C38.7 38.8802 38.0956 39.5735 37.35 39.5735H11.7C11.7 40.7138 12.5059 41.6382 13.5 41.6382H37.35C38.0956 41.6382 38.7 42.3315 38.7 43.1867C38.7 44.042 38.0956 44.7353 37.35 44.7353H13.5C11.0147 44.7353 9 42.4243 9 39.5735V8.60293ZM11.7 36.4765H36V8.60293C36 7.46262 35.1941 6.53822 34.2 6.53822H13.5C12.5059 6.53822 11.7 7.46262 11.7 8.60293V36.4765Z"
                                            fill="#1890FF"
                                        />
                                    </svg>
                                </div>
                                <div class="">
                                    <div class="mb-2 flex w-full">
                                        <div class="my-auto mr-2 text-sm font-medium">
                                            {{ courseItem.Code }} -
                                            {{ courseItem.Title }}
                                        </div>
                                        <CourseStatusBtn
                                            :status="getStatusNumber(courseItem)"
                                            :completed="courseItem.Complete"
                                            :completelink="
                                                appUrl(`spa/courses/add-details/${courseItem.Code}`)
                                            "
                                            :multiple="true"
                                        />
                                    </div>
                                    <div class="flex w-full space-x-2 text-xs">
                                        <div class="flex">
                                            <div class="mr-2 text-gray-400">TGA Title:</div>
                                            <div class="text-gray-700"></div>
                                        </div>
                                        <div class="flex">
                                            <div class="mr-2 text-gray-400">Component Type:</div>
                                            <div class="text-gray-700">
                                                {{ courseItem.ComponentType }}
                                            </div>
                                        </div>
                                        <div class="flex">
                                            <div class="mr-2 text-gray-400">Is Current:</div>
                                            <div class="capitalize text-gray-700">
                                                {{ courseItem.IsCurrent }}
                                            </div>
                                        </div>
                                        <div class="flex">
                                            <div class="mr-2 text-gray-400">Created Date:</div>
                                            <div class="text-gray-700">
                                                {{ courseItem.CreatedDate.DateTime }}
                                            </div>
                                        </div>
                                        <div class="flex">
                                            <div class="mr-2 text-gray-400">Updated Date:</div>
                                            <div class="text-gray-700">
                                                {{ courseItem.UpdatedDate.DateTime }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="float-right my-auto h-fit items-center space-x-2">
                                <div
                                    v-if="!courseItem.Added"
                                    class="my-auto flex h-fit items-center space-x-2"
                                >
                                    <button
                                        @click.prevent="showQuickAddForm(courseItem)"
                                        :disabled="addingCourse"
                                        class="flex items-center justify-between rounded-lg border border-primary-blue-300 bg-primary-blue-500 px-3 py-2 shadow hover:shadow-lg focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white"
                                        :class="{
                                            'disabled opacity-50': addingCourse,
                                        }"
                                    >
                                        <icon
                                            :name="'plus'"
                                            :width="16"
                                            :height="16"
                                            :fill="'#FFF'"
                                        />
                                        <div class="ml-2 text-sm uppercase text-white">
                                            {{ courseItem.adding ? 'Adding Course' : 'Add Course' }}
                                        </div>
                                    </button>
                                    <!--
                                    Old setup
                                    <button @click.prevent="showQuickAddForm(courseItem)" 
                                        :disabled="addingCourse"
                                        class="flex items-center justify-between rounded-lg border border-gray-300 bg-white px-3 py-2 shadow hover:shadow-lg focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-white"
                                        :class="{ 'disabled opacity-50' : addingCourse }"
                                        >
                                        <icon :name="'plus'" :width="16" :height="16" />
                                        <div class="ml-2 text-sm uppercase">
                                            Quick Add
                                        </div>
                                    </button>
                                    <Link :href="appUrl(`spa/courses/add-course/${courseItem.Code}`)"
                                        class="flex items-center justify-between rounded-lg border border-primary-blue-300 bg-primary-blue-500 px-3 py-2 shadow hover:shadow-lg focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white"
                                        :disabled="addingCourse"
                                        :class="{ 'disabled opacity-50' : addingCourse && !courseItem.adding }"
                                        @click.prevent="changeAddingState(courseItem)">
                                        <icon :name="'loading'" :width="16" :height="16" :fill="'#FFF'" v-if="courseItem.adding" />
                                        <icon :name="'plus'" :width="16" :height="16" :fill="'#FFF'" v-else />
                                        <div class="ml-2 text-sm uppercase text-white">
                                            {{ courseItem.adding ? "Adding Course" : "Add Course" }}
                                        </div>
                                    </Link>
                                    -->
                                </div>
                                <div v-else-if="courseItem.Deleted">
                                    <Link
                                        :href="appUrl(`spa/courses/add-course/${courseItem.Code}`)"
                                        class="flex items-center justify-between rounded-lg border border-primary-blue-300 bg-primary-blue-500 px-3 py-2 shadow hover:shadow-lg focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white"
                                        :disabled="addingCourse"
                                        :class="{
                                            'disabled opacity-50':
                                                addingCourse && !courseItem.adding,
                                        }"
                                        @click.prevent="changeAddingState(courseItem)"
                                    >
                                        <icon
                                            :name="'loading'"
                                            :width="16"
                                            :height="16"
                                            :fill="'#FFF'"
                                            v-if="courseItem.adding"
                                        />
                                        <icon
                                            :name="'sync'"
                                            :width="16"
                                            :height="16"
                                            :fill="'#FFF'"
                                            v-else
                                        />
                                        <div class="ml-2 text-sm uppercase text-white">
                                            {{
                                                courseItem.adding
                                                    ? 'Restoring Course'
                                                    : 'Restore this course'
                                            }}
                                        </div>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="course-pagination my-4">
                        <Pager
                            :skip="(pageNumber - 1) * perPage"
                            :take="perPage"
                            :total="courses.Count"
                            @changedPage="handlePageChange"
                            @changedPageSize="handlePagesizeChange"
                            :button-count="5"
                            :info="true"
                            :previous-next="true"
                            :type="'numeric'"
                            :page-sizes="[10, 20, 50, 100, 150, 200]"
                            :pager-render="'myTemplate'"
                            :responsive="false"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <PagerTemplate
                                    :current-page="props.current"
                                    :page-size="props.perpage"
                                    :skip="props.skip"
                                    :take="props.take"
                                    :size-defs="props.pageSizes"
                                    :total-pages="Math.ceil(props.total / props.take)"
                                    :total-items="props.total"
                                />
                            </template>
                        </Pager>
                    </div>
                </div>
                <div v-else class="items-center py-6 text-base leading-6 text-gray-500">
                    <div v-if="hasSearched">
                        No courses found matching your search criteria. Try with different search
                        text.
                    </div>
                    <!--
                    <div v-else>
                        Search for the courses by Course Title or Course Code.
                    </div>
                    -->
                </div>
            </GlobalContextLoader>
        </div>
        <QuickAddForm
            :course="localCourseUnits"
            v-model:visible="QuickAdd.QuickAddFormVisible"
            v-model:loading="QuickAdd.dataLoading"
            v-model:custom="QuickAdd.customCourse"
            :coursetypes="courseTypes"
            :searchText="searchText"
            :page="pageNumber"
            :take="perPage"
            @added="handedSaved"
            @saved="handedSaved"
            @closed="closeQuickAddForm"
        />
    </Layout>
</template>
<script setup>
import { onMounted, reactive, computed, ref, watch, onUpdated } from 'vue';
import { Pager } from '@progress/kendo-vue-data-tools';
import Layout from '../Layouts/Layout';
import { router, Head, Link, usePage } from '@inertiajs/vue3';
import PageTitleContent from '../Layouts/PageTitleContent';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import SecondaryButton from '@spa/components/Buttons/SecondaryButton.vue';
import CourseStatusBtn from './commons/CourseStatusButtons.vue';
import QuickAddForm from './QuickAddFormNew.vue';
import PagerTemplate from '@spa/components/ListViewPagination.vue';
import SkeletonList from '@spa/components/Skeleton/SkeletonList.vue';
import { useCoursesStore } from '@spa/stores/modules/courses';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import globalHelper from '@spa/plugins/global-helper';

const $page = usePage();

const props = defineProps({
    filters: Object,
    user: Object,
    courseTypes: Object,
    course: Object,
    courses: Object,
    found: Object,
});

const flashError = computed(() => $page.props.flash.error);
if (flashError.value) {
    globalHelper.methods.showPopupError(flashError.value);
}
const store = useCoursesStore();
const loaderStore = useLoaderStore();

const getCourseListUrl = computed(() => {
    return route('spa.courses.index', store.courseListFilters);
});

const searchText = ref(props.filters?.search || '');
const pageNumber = ref(props.filters?.page || 1);
const searching = ref(false);
const perPage = ref(props.filters?.show || 10);
const quickAddDialog = ref(false);
const loading = ref(false);
const addingCourse = ref(false);
const textOk = ref(false);
const hasSearched = computed(() => {
    return (props.filters?.search || '').length > 0 ? true : false;
});

const QuickAdd = reactive({
    QuickAddFormVisible: false,
    CourseData: null,
    dataLoading: false,
    customCourse: false,
});

const makeRequest = () => {
    if (searchText.value && searching.value == false && checkSearchTextValid.value == false) {
        router.get(
            route('spa.courses.add'),
            {
                search: searchText.value,
                page: pageNumber.value,
                show: perPage.value,
            },
            {
                only: ['courses', 'filters'],
                preserveState: true,
                replace: true,
                onBefore: (visit) => {
                    loading.value = true;
                    loaderStore.startContextLoading('tga-course-list');
                },
                onStart: (visit) => {
                    searching.value = true;
                    loading.value = true;
                },
                onFinish: (visit) => {
                    searching.value = false;
                    loading.value = false;
                    loaderStore.stopContextLoading('tga-course-list');
                },
            }
        );
    } else {
        //showPopupWarning("Provide some text to search", "Empty Search Parameter");
    }
};
const SearchCourses = () => {
    addingCourse.value = false;
    pageNumber.value = 1;
    makeRequest();
};
const handlePageChange = (e) => {
    addingCourse.value = false;
    pageNumber.value = e;
    makeRequest();
};
const handlePagesizeChange = (e) => {
    addingCourse.value = false;
    perPage.value = e;
    makeRequest();
};

const handleKeypressEvent = (e) => {
    if (e.keyCode == 13) SearchCourses();
    return;
};

const localCourseUnits = ref(props.course);
//watch for the changes in course units
watch(
    () => props.course,
    (newCourse) => {
        localCourseUnits.value = newCourse ? newCourse : [];
    }
);
const checkSearchTextValid = computed(() => {
    const pattern = /^[a-zA-Z0-9 ]*$/;
    return !pattern.test(searchText.value);
});
const changeAddingState = (course) => {
    if (addingCourse.value == true) return false;
    course.adding = true;
    addingCourse.value = true;
    return true;
};
const showQuickAddForm = (selectedCourse) => {
    QuickAdd.CourseData = selectedCourse;
    QuickAdd.QuickAddFormVisible = true;
    if (selectedCourse.ComponentType !== 'Unit') {
        localCourseUnits.value = null;
        QuickAdd.dataLoading = true;
        router.get(
            route('spa.courses.add'),
            {
                course: selectedCourse.Code,
                search: searchText.value,
                page: pageNumber.value,
                show: perPage.value,
            },
            {
                only: ['course', 'filters'],
                preserveState: true,
                replace: true,
                onStart: (visit) => {
                    QuickAdd.dataLoading = true;
                },
                onSuccess: (page) => {
                    QuickAdd.dataLoading = false;
                },
            }
        );
    } else {
        console.log(selectedCourse);
        QuickAdd.QuickAddFormVisible = true;
        localCourseUnits.value = selectedCourse;
    }
};
const closeQuickAddForm = () => {
    QuickAdd.CourseData = null;
    QuickAdd.QuickAddFormVisible = false;
    QuickAdd.customCourse = false;
    localCourseUnits.value = null;
    return;
    //QuickAdd.CourseData = null;
    //QuickAdd.QuickAddFormVisible = false;
};
const handedSaved = () => {
    closeQuickAddForm();
    return;
};
const getStatusNumber = (course) => {
    if (course.Added) {
        return course.Deleted ? 22 : 2;
    }
    return 3;
};
const hasShortCourse = computed(() => {
    return props.courses.Results.find(
        (type) => type.ComponentType === 'Unit' || type.ComponentType === 'SkillSet'
    );
});
const isShortCourseEnabled = computed(() => {
    return props.courseTypes.filter((type) => type.value === 16).length > 0;
});
</script>
