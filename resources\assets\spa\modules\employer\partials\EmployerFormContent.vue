<template>
    <div class="grid grid-cols-2 gap-4">
        <!-- Basic Information Section -->
        <div class="col-span-2">
            <h3 class="text-lg font-semibold mb-4">Basic Information</h3>
        </div>
        
        <!-- Employer Name -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Employer Name <span class="text-red-500">*</span>
            </label>
        </div>
        
        <!-- Trading Name -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Trading Name <span class="text-red-500">*</span>
            </label>
        </div>
        
        <!-- Contact Person -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Contact Person <span class="text-red-500">*</span>
            </label>
        </div>
        
        <!-- ABN -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                ABN <span class="text-red-500">*</span>
            </label>
        </div>
        
        <!-- Contact Information Section -->
        <div class="col-span-2 mt-6">
            <h3 class="text-lg font-semibold mb-4">Contact Information</h3>
        </div>
        
        <!-- Email -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Email <span class="text-red-500">*</span>
            </label>
        </div>
        
        <!-- Phone -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Phone <span class="text-red-500">*</span>
            </label>
        </div>
        
        <!-- Mobile -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Mobile
            </label>
        </div>
        
        <!-- Fax -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Fax
            </label>
        </div>
        
        <!-- Address Information Section -->
        <div class="col-span-2 mt-6">
            <h3 class="text-lg font-semibold mb-4">Address Information</h3>
        </div>
        
        <!-- Address -->
        <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Address <span class="text-red-500">*</span>
            </label>
        </div>
        
        <!-- Suburb -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Suburb <span class="text-red-500">*</span>
            </label>
        </div>
        
        <!-- State -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                State
            </label>
        </div>
        
        <!-- Postcode -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Postcode <span class="text-red-500">*</span>
            </label>
        </div>
        
        <!-- Country -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Country
            </label>
        </div>
        
        <!-- Additional Information Section -->
        <div class="col-span-2 mt-6">
            <h3 class="text-lg font-semibold mb-4">Additional Information</h3>
        </div>
        
        <!-- Industry -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Industry
            </label>
        </div>
        
        <!-- Status -->
        <div class="col-span-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Status <span class="text-red-500">*</span>
            </label>
        </div>
    </div>
</template>

<script setup>
// This component provides the form layout structure
// The actual form fields are handled by AsyncForm
</script>
