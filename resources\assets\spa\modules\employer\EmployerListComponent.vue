<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="false"
        :has-create-action="true"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
        :actions="['edit', 'delete']"
    >
        <template #body-cell-employer_name="{ props }">
            {{ props.dataItem.employer_name }}
        </template>
        <template #body-cell-contact_person="{ props }">
            {{ props.dataItem.contact_person }}
        </template>
        <template #body-cell-email="{ props }">
            {{ props.dataItem.email }}
        </template>
        <template #body-cell-status="{ props }">
            {{ props.dataItem.status_name }}
        </template>
        <template #body-cell-industry_name="{ props }">
            {{ props.dataItem.industry_name || '--' }}
        </template>
        <template #body-cell-phone="{ props }">
            {{ props.dataItem.phone }}
        </template>
        <template #body-cell-trading_name="{ props }">
            {{ props.dataItem.trading_name }}
        </template>
        <template #body-cell-state="{ props }">
            {{ props.dataItem.state || '--' }}
        </template>
    </AsyncGrid>
    <EmployerForm />
</template>

<script setup>
import { onMounted } from 'vue';
import { useEmployerStore } from '@spa/stores/modules/employer/employerStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import EmployerForm from '@spa/modules/employer/EmployerForm.vue';

const store = useEmployerStore();

const columns = [
    {
        name: 'employer_name',
        title: 'Name',
        field: 'employer_name',
        sortable: true,
    },
    {
        name: 'contact_person',
        title: 'Contact Person',
        field: 'contact_person',
        sortable: true,
    },
    {
        name: 'email',
        title: 'Email',
        field: 'email',
        sortable: true,
    },
    {
        name: 'status',
        title: 'Status',
        field: 'status_name',
        sortable: true,
    },
    {
        name: 'industry_name',
        title: 'Industry',
        field: 'industry_name',
        sortable: true,
    },
    {
        name: 'phone',
        title: 'Phone',
        field: 'phone',
        sortable: true,
    },
    {
        name: 'trading_name',
        title: 'Trading Name',
        field: 'trading_name',
        sortable: true,
    },
    {
        name: 'state',
        title: 'State',
        field: 'state',
        sortable: true,
    },
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    // initFilters();
});
</script>