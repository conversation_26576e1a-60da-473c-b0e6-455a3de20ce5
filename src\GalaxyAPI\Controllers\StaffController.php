<?php

namespace GalaxyAPI\Controllers;

use App\Model\Staff;
use GalaxyAPI\Requests\EmptyRequest;
use GalaxyAPI\Resources\StaffResource;
use Illuminate\Support\Facades\Auth;

class StaffController extends CrudBaseController
{
    public function __construct()
    {
        $this->scopeWithValue = [
            'collegeId' => Auth::user()?->college_id,
        ];

        parent::__construct(
            model: Staff::class,
            storeRequest: EmptyRequest::class,
            updateRequest: EmptyRequest::class,
            resource: StaffResource::class,
        );
    }
}
