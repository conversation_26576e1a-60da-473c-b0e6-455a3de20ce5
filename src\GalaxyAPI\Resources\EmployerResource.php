<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Config;

class EmployerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $arrStatus = Config::get('constants.arrStatus');
        $arrIndustries = Config::get('constants.arrTraningOrgazinationIndustryCode');

        return [
            'id' => $this->id,
            'employer_name' => $this->employer_name,
            'trading_name' => $this->trading_name,
            'contact_person' => $this->contact_person,
            'ABN' => $this->ABN,
            'industries_id' => $this->industries_id,
            'industry_name' => $arrIndustries[$this->industries_id] ?? null,
            'address' => $this->address,
            'suburb' => $this->suburb,
            'country_id' => $this->country_id,
            'email' => $this->email,
            'fax' => $this->fax,
            'postcode' => $this->postcode,
            'state' => $this->state,
            'phone' => $this->phone,
            'mobile' => $this->mobile,
            'status' => $this->status,
            'status_name' => $arrStatus[$this->status] ?? null,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
