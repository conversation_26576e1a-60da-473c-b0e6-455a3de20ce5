<?php

namespace GalaxyAPI\Resources;

use Config;
use Illuminate\Http\Resources\Json\JsonResource;

class EmployerResource extends JsonResource
{
    public function toArray($request)
    {
        $arrStatus = Config::get('constants.arrStatus');
        $arrIndustries = Config::get('constants.arrTraningOrgazinationIndustryCode');

        return [
            'id' => $this->id,
            'employer_name' => $this->employer_name,
            'trading_name' => $this->trading_name,
            'contact_person' => $this->contact_person,
            'ABN' => $this->ABN,
            'address' => $this->address,
            'industries_id' => $this->industries_id,
            'industry_name' => $arrIndustries[$this->industries_id] ?? null,
            'suburb' => $this->suburb,
            'country_id' => $this->country_id,
            'email' => $this->email,
            'fax' => $this->fax,
            'status' => $this->status,
            'status_name' => $arrStatus[$this->status] ?? null,
            'postcode' => $this->postcode,
            'state' => $this->state,
            'phone' => $this->phone,
            'mobile' => $this->mobile,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
