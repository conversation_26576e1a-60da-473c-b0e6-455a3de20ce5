<script setup>
import { Head } from '@inertiajs/vue3';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import LeaveInfoListComponent from '@spa/modules/teachers/leave-info/LeaveInfoListComponent.vue';
</script>

<template>
    <Layout :no-spacing="true">
        <Head title="Leave Info" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Leave Info" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <LeaveInfoListComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
