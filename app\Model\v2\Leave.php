<?php

namespace App\Model\v2;

use App\Model\Staff;
use Auth;
use Illuminate\Database\Eloquent\Model;

class Leave extends Model
{
    protected $table = 'rto_leave_info';

    protected $fillable = [
        'college_id',
        'staff_id',
        'user_position',
        'from_date',
        'to_date',
        'comment',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    public function staff()
    {
        return $this->belongsTo(Staff::class, 'staff_id');
    }

    public function afterCreateProcess()
    {
        $authId = Auth::id();
        $staffInfo = getCurrentStaff();
        $currentRole = getCurrentRole();
        $this->update([
            'user_position' => $currentRole->role_name,
            'staff_id' => $staffInfo->id,
            'created_by' => $authId,
            'updated_by' => $authId,
        ]);
    }

    public function afterUpdateProcess()
    {
        $authId = Auth::id();
        $this->update([
            'updated_by' => $authId,
        ]);
    }

    public function scopeQuery(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('comment', 'like', "%{$value}%");
    }

    public function scopeStaffId(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('staff_id', $value);
    }
}
