<template>
    <div>
        <k-dialog
            v-if="visibleDialog"
            :append-to="appendTo"
            :title-render="titleRender"
            :dialog-class="dialogClass"
            :min-width="minWidth"
            :width="width"
            :wrapper-id="wrapperId"
            @close="handleCancel"
            @overlayclick="handleOverlayClick"
        >
            <template v-slot:titleTemplate="{ props, listeners }">
                <div class="flex w-full justify-between">
                    <slot name="title"></slot>
                    <!-- <button
                        type="button"
                        class="text-gray-400 hover:text-gray-500"
                        @click="handleResize"
                    >
                        <IconExpand class="size-6 text-white" />
                    </button> -->
                </div>
            </template>
            <div v-if="$slots.content" :class="contentClass">
                <slot name="content"></slot>
            </div>
            <dialog-actions-bar v-if="fixedActionBar">
                <div
                    class="flex w-full flex-wrap gap-4 lg:gap-0"
                    :class="$slots.summary ? 'justify-between' : 'justify-end'"
                >
                    <div v-if="$slots.summary" :class="summaryClass">
                        <slot name="summary"></slot>
                    </div>
                    <div class="flex flex-wrap justify-start gap-4 lg:justify-end">
                        <k-button
                            :variant="'secondary'"
                            :size="'sm'"
                            @click="handleCancel"
                            v-if="secondaryBtnLabel.length > 0"
                            ><span class="capitalize">{{ secondaryBtnLabel }}</span></k-button
                        >
                        <k-button
                            v-if="existTertiary"
                            @click="handleSaveAndClose"
                            :class="{ disabled: isDisabled }"
                            :variant="'primary'"
                            :size="'sm'"
                            :pt="{ root: 'min-w-[100px]' }"
                        >
                            <span v-if="isTertiarySubmitting">
                                <icon name="loading-spinner" :width="'16'" :height="'16'" />
                            </span>
                            <span class="capitalize">{{ tertiaryBtnLabel }}</span></k-button
                        >
                        <k-button
                            @click="handleSave"
                            :class="{ disabled: isDisabled }"
                            :variant="'primary'"
                            :size="'sm'"
                            :pt="{ root: 'min-w-[10rem]' }"
                            v-if="primaryBtnLabel.length > 0"
                        >
                            <span v-if="isSubmitting">
                                <icon name="loading-spinner" :width="'16'" :height="'16'" />
                            </span>
                            <span class="capitalize">{{ primaryBtnLabel }}</span></k-button
                        >
                    </div>
                </div>
            </dialog-actions-bar>
            <slot name="footer" v-else> </slot>

            <tw-loader
                :pt="{
                    root: 'jusify-center absolute inset-0 z-50 flex h-full w-full items-center bg-white/80',
                }"
                :size="24"
                :showText="false"
                :show="isTertiarySubmitting || isSubmitting"
            />
        </k-dialog>
    </div>
</template>
<script>
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
import Button from '@spa/components/Buttons/Button.vue';
import { twMerge } from 'tailwind-merge';
import Loader from '../Loader/Spinner.vue';
import {
    IconArrowExpand24Regular,
    IconArrowMinimize24Regular,
} from '@iconify-prerendered/vue-fluent';

function injectStyle(css) {
    const style = document.createElement('style');
    style.innerHTML = css;
    document.head.appendChild(style);
}

export default {
    components: {
        'k-dialog': Dialog,
        'dialog-actions-bar': DialogActionsBar,
        'k-button': Button,
        'tw-loader': Loader,
        IconExpand: IconArrowExpand24Regular,
        IconMinimize: IconArrowMinimize24Regular,
    },
    props: {
        visibleDialog: Boolean,
        title: { default: 'Title' },
        width: { default: 600 },
        fixedActionBar: { default: false },
        hideOnOverlayClick: { default: false },
        appendTo: { default: 'body' },
        wrapperClass: String,
        wrapperId: String,
        primaryBtnLabel: { default: '' },
        secondaryBtnLabel: { default: 'Cancel' },
        tertiaryBtnLabel: { default: 'Save and Close' },
        isDisabled: { default: false },
        isSubmitting: { default: false },
        isTertiarySubmitting: { default: false },
        existTertiary: { default: false },
        pt: {
            type: Object,
            default: {},
        },
        maxWidth: {
            type: [String, Number],
            default: 600,
        },
        dialogStyle: {
            type: [String, Object],
            default: () => ({}),
        },
        minWidth: {
            type: [String, Number],
            default: 300,
        },
        position: {
            type: String,
            default: 'right', // 'left' | 'right' | 'center'
        },
    },
    emits: ['drawerclose', 'draweradded', 'drawersaved'],
    data: function () {
        return {
            titleRender: 'titleTemplate',
            closing: false,
        };
    },
    mounted() {
        injectStyle(
            `.max-w-\\[${this.maxWidth}\\]{ max-width: ${this.maxWidth};}
        `
        );
        window.addEventListener('keydown', this.handleKeydown);
    },
    computed: {
        dialogClass() {
            const { position, closing, pt } = this;

            const baseClasses = 'tw-content-p-0 absolute top-0 ' + `max-w-[${this.maxWidth}]`;
            const transitions = closing
                ? `tw-slideout-to-${position}`
                : `tw-slidein-from-${position}`;

            const positionClasses = {
                left: `tw-sidebar-drawer h-screen left-0 ${transitions}`,
                right: `tw-sidebar-drawer h-screen right-0 ${transitions}`,
                center: 'tw-dialog',
            };

            return twMerge(`${baseClasses} ${positionClasses[position]}`, pt.root);
        },
        contentClass() {
            return twMerge('space-y-6 px-6 py-6 h-full overflow-y-auto', this.pt.content);
        },
        summaryClass() {
            return twMerge('inline-flex items-center', this.pt.content);
        },
    },
    methods: {
        handleOverlayClick(e) {
            if (this.hideOnOverlayClick) {
                this.handleCancel(e);
            }
        },
        handleSave(e) {
            if (!this.isDisabled && !this.isSubmitting) {
                this.$emit('drawersaved', e);
            }
        },
        handleCancel(e) {
            this.closing = true;
            setTimeout(() => {
                this.$emit('drawerclose', e);

                this.closing = false;
            }, 300);
        },
        handleSaveAndClose(e) {
            if (!this.isDisabled && !this.isTertiarySubmitting) {
                this.$emit('drawersavedandclosed', e);
            }
        },
        handleKeydown(event) {
            if (event.key === 'Escape' || event.key === 'Esc') {
                this.handleCancel();
            }
        },
    },
};
</script>
<style scoped></style>
