<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LeaveResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'staff_id' => $this->staff_id,
            'staff' => $this->whenLoaded('staff', function () {
                return [
                    'id' => $this->staff->id,
                    'full_name' => $this->staff->name_title.' '.$this->staff->first_name.' '.$this->staff->last_name,
                ];
            }),
            'user_position' => $this->user_position,
            'from_date' => $this->from_date,
            'to_date' => $this->to_date,
            'comment' => $this->comment,
            'created_at' => $this->created_at,
        ];
    }
}
