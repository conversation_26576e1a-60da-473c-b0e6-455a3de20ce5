<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LeaveRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'from_date' => 'required|date',
            'to_date' => 'required|after_or_equal:from_date',
            'comment' => 'required|string|max:255',
        ];
    }

    public function prepareForValidation()
    {
        $currentRole = getCurrentRole();
        if (! in_array($currentRole->role_name, ['Staff', 'Teacher'])) {
            throw new \Exception('Unauthorized');
        }
    }
}
