<template>
    <FormWrapper
        :visibleDialog="store.formDialog"
        :hideOnOverlayClick="true"
        :fixedActionBar="!isKendo"
        :width="'50%'"
        :maxWidth="maxWidth"
        :style="{
            maxWidth: maxWidth,
        }"
        :primaryBtnLabel="submitText"
        :secondaryBtnLabel="'Cancel'"
        :isDisabled="store.loading"
        :isSubmitting="store.loading"
        @drawerclose="store.formDialog = false"
        :position="dialogPosition"
        :pt="{ content: 'p-0' }"
    >
        <template #title>
            <div class="text-lg font-medium">{{ dialogTitle }}</div>
        </template>
        <template #content>
            <component
                :is="isKendo ? KendoForm : 'form'"
                :initial-values="initialValues"
                @submitclick="handleSubmit"
                :ref="store.formRef"
            >
                <form-element class="h-full">
                    <fieldset class="flex h-full flex-col">
                        <div class="flex-1 overflow-y-auto">
                            <slot v-if="override" />
                            <FormElement v-else>
                                <fieldset class="h-screen-header flex flex-col">
                                    <div class="flex-1 overflow-y-auto px-6 py-4">
                                        <div :class="getLayoutClass">
                                            <template
                                                v-for="(field, key) in fieldsConfig"
                                                :key="key"
                                            >
                                                <div
                                                    v-if="shouldShowField(field)"
                                                    :class="field.colClass"
                                                >
                                                    <Field
                                                        :id="key"
                                                        :name="key"
                                                        :label="field.label"
                                                        :type="field.type"
                                                        v-model="store.formData[key]"
                                                        v-bind="getFieldProps(field)"
                                                        :component="
                                                            field.replace
                                                                ? `${key}Template`
                                                                : (field.component ??
                                                                  `${field.type}Template`)
                                                        "
                                                    >
                                                        <template
                                                            v-if="!field.replace"
                                                            #[`${field.type}Template`]="{ props }"
                                                        >
                                                            <component
                                                                :is="componentMap[field.type]"
                                                                v-bind="props"
                                                                @change="props.onChange"
                                                                @blur="props.onBlur"
                                                                @focus="props.onFocus"
                                                            />
                                                        </template>
                                                        <template
                                                            v-else
                                                            #[`${key}Template`]="{ props }"
                                                        >
                                                            <slot
                                                                :name="`field-${key}`"
                                                                :props="{ ...props }"
                                                            ></slot>
                                                        </template>
                                                    </Field>
                                                </div>

                                                <!-- Custom slot for complex fields -->
                                                <div
                                                    v-if="field.type === 'custom'"
                                                    :class="field.colClass"
                                                >
                                                    <slot
                                                        :name="`fieldset-${key}`"
                                                        :field="field"
                                                        :value="store.formData[key]"
                                                        :update="(val) => updateField(key, val)"
                                                    ></slot>
                                                </div>
                                            </template>
                                        </div>
                                    </div>

                                    <!-- Submit/Cancel Buttons -->
                                    <div
                                        v-if="!hasCustomSubmit"
                                        class="flex items-center justify-end border-t px-6 py-3"
                                    >
                                        <Button
                                            type="submit"
                                            theme-color="primary"
                                            :disabled="submitting"
                                        >
                                            {{ submitText || 'Submit' }}
                                        </Button>
                                        <Button
                                            v-if="showCancel"
                                            type="button"
                                            variant="secondary"
                                            class="ml-2"
                                            @click="emit('cancel')"
                                        >
                                            Cancel
                                        </Button>
                                    </div>
                                    <slot v-else name="customSubmit" />
                                </fieldset>
                            </FormElement>
                        </div>

                        <div class="flex items-center justify-end gap-4 border-t px-6 py-3">
                            <Button
                                size="base"
                                type="submit"
                                variant="primary"
                                class="min-w-[100px]"
                            >
                                <span>Save</span>
                            </Button>
                            <Button
                                size="base"
                                variant="secondary"
                                @click="store.formDialog = false"
                            >
                                <span>Cancel</span>
                            </Button>
                        </div>
                    </fieldset>
                </form-element>
            </component>
        </template>
    </FormWrapper>
</template>
<script setup>
import { ref, watch, defineExpose, computed } from 'vue';
import { Form as KendoForm, Field, FormElement } from '@progress/kendo-vue-form';
import Button from '@spa/components/Buttons/Button.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormCheckbox from '@spa/components/KendoInputs/FormCheckbox.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import FormNumericInput from '@spa/components/KendoInputs/FormNumericInput.vue';
import { twMerge } from 'tailwind-merge';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import FormWrapper from '@spa/components/KendoModals/SidebarDrawer.vue';

const props = defineProps({
    store: Object,
    dialogTitle: String,
    dialogPosition: {
        type: String,
        default: 'right',
    },
    override: {
        type: Boolean,
        default: true,
    },
    initialValues: {
        type: Object,
        default: () => ({}),
    },
    validator: {
        type: Function,
        default: () => () => ({}),
    },
    formConfig: {
        type: Object,
        required: false,
    },
    submitText: {
        type: String,
        default: 'Submit',
    },
    cancelText: {
        type: String,
        default: 'Cancel',
    },
    showCancel: {
        type: Boolean,
        default: true,
    },
    hasCustomSubmit: {
        type: Boolean,
        default: false,
    },

    type: {
        type: String,
        default: 'kendo', // default or kendo
    },
    layout: {
        type: Object,
        default: () => ({
            cols: 1,
            gap: 20,
        }),
    },
    orientation: {
        type: String,
        default: 'vertical', // vertical or horizontal
    },
    maxWidth: {
        default: '600px',
    },
});

const componentMap = {
    text: FormInput,
    email: FormInput,
    password: FormInput,
    textarea: FormTextArea,
    select: FormDropDown,
    checkbox: FormCheckbox,
    radio: FormRadioGroup,
    date: FormDatePicker,
    number: FormNumericInput,
};

const emit = defineEmits(['submit', 'cancel']);

const submitting = ref(false);

const isKendo = computed(() => {
    return props.type === 'kendo';
});

const initializeForm = () => {
    const initialData = {};
    Object.keys(props.formConfig).forEach((key) => {
        const field = props.formConfig[key];
        initialData[key] =
            props.initialData[key] !== undefined
                ? props.initialData[key]
                : field.default !== undefined
                  ? field.default
                  : field.type === 'checkbox'
                    ? false
                    : null;
    });
    props.store.formData = initialData;
};

const fieldsConfig = computed(() => {
    const updatedFields = {};

    Object.keys(props.formConfig).forEach((key) => {
        const field = props.formConfig[key];
        updatedFields[key] = {
            ...field,
            id: key,
            name: key,
            colClass: `col-span-${field.colSpan || 1}`,
            replace: field.replace ?? false,
            validator: field.validate ? field.validate : field.required ? requiredtrue : null,
        };
    });

    return updatedFields;
});

const getFieldProps = (field) => {
    const commonProps = {
        required: field.required,
        placeholder: field.placeholder || '',
        disabled: field.disabled,
        validator: field.validator,
    };

    const typeSpecificProps = {
        select: {
            'data-items': field.options || [],
            'text-field': field.textField || 'text',
            'value-field': field.valueField || 'value',
            placeholder: field.placeholder || 'Select an option',
            'default-item': {
                text: field.placeholder || 'Select an option',
                value: null,
            },
        },
        date: {
            format: field.format || 'MM/dd/yyyy',
        },
        number: {
            min: field.min,
            max: field.max,
            step: field.step || 1,
        },
    };

    return { ...commonProps, ...typeSpecificProps[field.type] };
};

const handleSubmit = (e) => {
    if (!e.isValid) return;
    props.store.submitFormData();
};

const shouldShowField = (field) => {
    if (typeof field.visible === 'function') {
        return field.visible(props.store.formData);
    }
    return field.visible !== false;
};

const updateField = (key, value) => {
    props.store.formData[key] = value;
};

const getLayoutClass = computed(() => {
    return twMerge('grid', `grid-cols-${props.layout.cols}`, `gap-${props.layout.gap / 4}`);
});

watch(
    () => props.initialData,
    (newVal) => {
        props.store.formData = { ...props.store.formData, ...newVal };
    },
    { deep: true }
);
</script>
<style lang=""></style>
