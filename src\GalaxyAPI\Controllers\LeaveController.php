<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\Leave;
use GalaxyAPI\Requests\LeaveRequest;
use GalaxyAPI\Resources\LeaveResource;

class LeaveController extends CrudBaseController
{
    public function __construct()
    {
        $this->withAll = [
            'staff',
        ];
        $this->loadAll = [
            'staff',
        ];
        $staffInfo = getCurrentStaff();
        $this->scopeWithValue = [
            'staffId' => $staffInfo->id,
        ];
        parent::__construct(
            model: Leave::class,
            storeRequest: LeaveRequest::class,
            updateRequest: LeaveRequest::class,
            resource: LeaveResource::class,
        );
    }
}
