<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete']"
    >
        <template #body-cell-staff="{ props }">
            <div class="p-2">
                {{ props.dataItem?.staff?.full_name }}
            </div>
        </template>
    </AsyncGrid>
    <LeaveInfoForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted, watch } from 'vue';
import { useLeaveInfoStore } from '@spa/stores/modules/teachers/useLeaveInfoStore.js';
import LeaveInfoForm from '@spa/modules/teachers/leave-info/LeaveInfoForm.vue';
const store = useLeaveInfoStore();
const columns = [
    {
        name: 'staff_id',
        title: 'Staff ID',
        field: 'staff_id',
        sortable: true,
    },
    {
        name: 'staff',
        title: 'Staff Name',
        field: 'staff',
        sortable: false,
        replace: true,
    },
    {
        name: 'from_date',
        title: 'Requested From',
        field: 'from_date',
        sortable: true,
    },
    {
        name: 'to_date',
        title: 'Requested To',
        field: 'to_date',
        sortable: true,
    },
    {
        name: 'comment',
        title: 'Comment',
        field: 'comment',
        sortable: true,
        columnFilter: true,
    },
];
const initFilters = () => {
    store.filters = {
        hasID: true,
        hasUsi: 'all',
        usiStatus: 'all',
        courseType: [],
    };
};
onMounted(() => {
    initFilters();
});
</script>
