<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EmployerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'employer_name' => 'required|string|max:255',
            'trading_name' => 'required|string|max:255',
            'contact_person' => 'required|string|max:255',
            'ABN' => 'required|string|max:255',
            'address' => 'required|string',
            'suburb' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'postcode' => 'required|string|max:10',
            'phone' => 'required|string|max:20',
            'industries_id' => 'nullable|string',
            'country_id' => 'nullable|integer',
            'fax' => 'nullable|string|max:20',
            'state' => 'nullable|string|max:255',
            'mobile' => 'nullable|string|max:20',
            'status' => 'required|integer',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'employer_name.required' => 'Employer name is required.',
            'trading_name.required' => 'Trading name is required.',
            'contact_person.required' => 'Contact person is required.',
            'ABN.required' => 'ABN is required.',
            'address.required' => 'Address is required.',
            'suburb.required' => 'Suburb is required.',
            'email.required' => 'Email is required.',
            'email.email' => 'Please enter a valid email address.',
            'postcode.required' => 'Postcode is required.',
            'phone.required' => 'Phone number is required.',
            'status.required' => 'Status is required.',
        ];
    }
}
